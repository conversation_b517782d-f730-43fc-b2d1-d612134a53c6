"use client"

import { useState, useEffect, useCallback, useMemo } from "react"
import { useRouter } from "next/navigation"
import { searchAllEntities } from "@/lib/actions"
import { debounce } from "lodash"

export type SearchResult = {
  id: string
  title: string
  subtitle?: string
  href: string
  type: "employee" | "department" | "manager" | "period" | "navigation" | "pto"
  icon: string
}

export function useGlobalSearch() {
  const [isOpen, setIsOpen] = useState(false)
  const [query, setQuery] = useState("")
  const [results, setResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const [recentSearches, setRecentSearches] = useState<SearchResult[]>([])
  const router = useRouter()

  // Debounced search function
  const debouncedSearch = useMemo(
    () => debounce(async (searchQuery: string) => {
      console.log('🔍 [FRONTEND DEBUG] Debounced search called with:', searchQuery)

      if (!searchQuery.trim()) {
        console.log('🔍 [FRONTEND DEBUG] Empty query, showing recent searches')
        setResults(recentSearches.slice(0, 5))
        setLoading(false)
        return
      }

      console.log('🔍 [FRONTEND DEBUG] Starting search for:', searchQuery)
      setLoading(true)
      try {
        const searchResults = await searchAllEntities(searchQuery)
        console.log('🔍 [FRONTEND DEBUG] Search results received:', searchResults)
        setResults(searchResults)
      } catch (error) {
        console.error("Search failed:", error)
        setResults([])
      } finally {
        setLoading(false)
      }
    }, 300),
    [recentSearches]
  )

  // Handle search query changes
  useEffect(() => {
    debouncedSearch(query)
    return () => debouncedSearch.cancel()
  }, [query, debouncedSearch])

  // Load recent searches from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem("global-search-recent")
      if (stored) {
        setRecentSearches(JSON.parse(stored))
      }
    } catch (error) {
      console.error("Failed to load recent searches:", error)
    }
  }, [])

  // Save recent searches to localStorage
  const saveRecentSearch = useCallback((result: SearchResult) => {
    setRecentSearches(prev => {
      const filtered = prev.filter(item => item.id !== result.id)
      const updated = [result, ...filtered].slice(0, 10) // Keep last 10
      
      try {
        localStorage.setItem("global-search-recent", JSON.stringify(updated))
      } catch (error) {
        console.error("Failed to save recent search:", error)
      }
      
      return updated
    })
  }, [])

  // Handle result selection
  const selectResult = useCallback((result: SearchResult) => {
    saveRecentSearch(result)
    setIsOpen(false)
    setQuery("")
    router.push(result.href)
  }, [router, saveRecentSearch])

  // Keyboard shortcut handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "k" && (event.metaKey || event.ctrlKey)) {
        event.preventDefault()
        setIsOpen(prev => !prev)
      }
      
      if (event.key === "Escape" && isOpen) {
        event.preventDefault()
        setIsOpen(false)
        setQuery("")
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => document.removeEventListener("keydown", handleKeyDown)
  }, [isOpen])

  // Reset when closing
  useEffect(() => {
    if (!isOpen) {
      setQuery("")
      setResults(recentSearches.slice(0, 5))
    }
  }, [isOpen, recentSearches])

  return {
    isOpen,
    setIsOpen,
    query,
    setQuery,
    results: query.trim() ? results : recentSearches.slice(0, 5),
    loading,
    selectResult,
    showingRecent: !query.trim(),
  }
}