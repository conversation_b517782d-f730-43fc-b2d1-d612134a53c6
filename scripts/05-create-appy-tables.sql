-- Create appraisal tables with appy_ prefix
-- This creates the tables that the application expects

-- <PERSON>reate departments table
CREATE TABLE IF NOT EXISTS appy_departments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL UNIQUE,
  created_at timestamptz DEFAULT now()
);

-- Create employees table with manager_id directly on the table
CREATE TABLE IF NOT EXISTS appy_employees (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  email text UNIQUE,
  department_id uuid REFERENCES appy_departments(id),
  manager_id text, -- This will store Clerk user IDs
  compensation text NOT NULL CHECK (compensation IN ('hourly', 'monthly')),
  rate numeric(10,2) NOT NULL,
  active boolean DEFAULT true,
  created_at timestamptz DEFAULT now()
);

-- Create managers table (stores Clerk user IDs)
CREATE TABLE IF NOT EXISTS appy_managers (
  user_id text PRIMARY KEY, -- Clerk user ID
  name text NOT NULL,
  email text,
  created_at timestamptz DEFAULT now()
);

-- Create user roles table
CREATE TABLE IF NOT EXISTS appy_user_roles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id text NOT NULL, -- Clerk user ID
  role text NOT NULL CHECK (role IN ('super-admin', 'hr-admin', 'admin', 'manager', 'accountant')),
  created_at timestamptz DEFAULT now()
);

-- Create appraisal periods table
CREATE TABLE IF NOT EXISTS appy_appraisal_periods (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  start_date date NOT NULL,
  end_date date NOT NULL,
  status text DEFAULT 'draft',
  created_at timestamptz DEFAULT now()
);

-- Create appraisals table
CREATE TABLE IF NOT EXISTS appy_appraisals (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  employee_id uuid NOT NULL REFERENCES appy_employees(id),
  period_id uuid NOT NULL REFERENCES appy_appraisal_periods(id),
  manager_id text NOT NULL, -- Clerk user ID (no foreign key to appy_managers to allow flexibility)
  question_1 text,
  question_2 text,
  question_3 text,
  question_4 text,
  question_5 text,
  status text DEFAULT 'draft' CHECK (status IN ('draft', 'submitted')),
  submitted_at timestamptz,
  created_at timestamptz DEFAULT now(),
  UNIQUE(employee_id, period_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_appy_employees_department_id ON appy_employees(department_id);
CREATE INDEX IF NOT EXISTS idx_appy_employees_manager_id ON appy_employees(manager_id);
CREATE INDEX IF NOT EXISTS idx_appy_appraisals_employee_id ON appy_appraisals(employee_id);
CREATE INDEX IF NOT EXISTS idx_appy_appraisals_period_id ON appy_appraisals(period_id);
CREATE INDEX IF NOT EXISTS idx_appy_appraisals_manager_id ON appy_appraisals(manager_id);
CREATE INDEX IF NOT EXISTS idx_appy_appraisals_status ON appy_appraisals(status);

-- Enable Row Level Security
ALTER TABLE appy_departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_managers ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_appraisal_periods ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_appraisals ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (adjust based on your auth requirements)
CREATE POLICY "Enable read access for all users" ON appy_departments FOR SELECT USING (true);
CREATE POLICY "Enable read access for all users" ON appy_employees FOR SELECT USING (true);
CREATE POLICY "Enable read access for all users" ON appy_managers FOR SELECT USING (true);
CREATE POLICY "Enable read access for all users" ON appy_user_roles FOR SELECT USING (true);
CREATE POLICY "Enable read access for all users" ON appy_appraisal_periods FOR SELECT USING (true);
CREATE POLICY "Enable read access for all users" ON appy_appraisals FOR SELECT USING (true);

-- Create policies for write access (adjust based on your auth requirements)
CREATE POLICY "Enable insert for all users" ON appy_departments FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update for all users" ON appy_departments FOR UPDATE USING (true);

CREATE POLICY "Enable insert for all users" ON appy_employees FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update for all users" ON appy_employees FOR UPDATE USING (true);

CREATE POLICY "Enable insert for all users" ON appy_managers FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update for all users" ON appy_managers FOR UPDATE USING (true);

CREATE POLICY "Enable insert for all users" ON appy_user_roles FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update for all users" ON appy_user_roles FOR UPDATE USING (true);

CREATE POLICY "Enable insert for all users" ON appy_appraisal_periods FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update for all users" ON appy_appraisal_periods FOR UPDATE USING (true);

CREATE POLICY "Enable insert for all users" ON appy_appraisals FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update for all users" ON appy_appraisals FOR UPDATE USING (true);